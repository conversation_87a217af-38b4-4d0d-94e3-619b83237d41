import { get, del, post, put, getFunc, patch } from "../utils/request"

// 收藏API接口类型定义
export interface FavoriteItem {
  id: string
  owner_id: string
  creator_id?: string | null
  parent_id: string
  is_folder: boolean
  level: number
  order: number
  folder_name?: string | null
  created_at: string
  updated_at: string
  profile?: {
    id: string
    name: string
    research_areas: string[]
    scholar: string
  }
}

export interface FavoriteResponse {
  data: FavoriteItem | FavoriteItem[] | boolean
}

export interface ReorderRequest {
  favorite_id: string
  order: number
}

export interface CreateFolderRequest {
  parent_id?: string | null
}

export interface MoveToFolderRequest {
  favorite_id: string
  folder_id?: string | null
}

export interface RenameFolderRequest {
  folder_id: string
  name: string
}

// 1. 添加收藏
export const addFavorite = (profileId: string, headers?: Record<string, any>) => 
  post<FavoriteResponse>(`/api/v1/favorite/add/${profileId}`, {}, headers)

// 2. 移除收藏
export const removeFavorite = (favoriteId: string, headers?: Record<string, any>) => 
  del<FavoriteResponse>(`/api/v1/favorite/remove/${favoriteId}`, headers)

// 3. 重新排序
export const reorderFavorite = (data: ReorderRequest, headers?: Record<string, any>) => 
  post<FavoriteResponse>('/api/v1/favorite/reorder', data, headers)

// 4. 获取收藏列表
export const getFavoriteList = (
  folderId?: string, 
  filter: 'all' | 'folder' = 'all', 
  headers?: Record<string, any>
) => {
  const params: Record<string, any> = { filter }
  if (folderId) {
    params.folder_id = folderId
  }
  return getFunc<FavoriteResponse>('/api/v1/favorite/list', params, headers)
}

// 5. 创建文件夹
export const createFolder = (data: CreateFolderRequest, headers?: Record<string, any>) => 
  post<FavoriteResponse>('/api/v1/favorite/folder', data, headers)

// 6. 移动到文件夹
export const moveToFolder = (data: MoveToFolderRequest, headers?: Record<string, any>) => 
  patch<FavoriteResponse>('/api/v1/favorite/folder', data, headers)

// 7. 删除文件夹
export const deleteFolder = (folderId: string, headers?: Record<string, any>) => 
  del<FavoriteResponse>(`/api/v1/favorite/folder/${folderId}`, headers)

// 8. 重命名文件夹
export const renameFolder = (data: RenameFolderRequest, headers?: Record<string, any>) => 
  post<FavoriteResponse>('/api/v1/favorite/folder/rename', data, headers)

// 便捷方法：获取用户的认证头
export const getAuthHeaders = () => {
  // 从Firebase认证中获取当前用户的UID作为Bearer token
  if (process.client) {
    const { currentUser } = useFirebaseAuth()
    if (currentUser.value?.uid) {
      return {
        'Authorization': `Bearer ${currentUser.value.uid}`
      }
    }
  }
  return {}
}

// 便捷方法：带认证的API调用
export const favoriteApi = {
  // 添加收藏
  add: (profileId: string) => addFavorite(profileId, getAuthHeaders()),
  
  // 移除收藏
  remove: (favoriteId: string) => removeFavorite(favoriteId, getAuthHeaders()),
  
  // 重新排序
  reorder: (favoriteId: string, order: number) => 
    reorderFavorite({ favorite_id: favoriteId, order }, getAuthHeaders()),
  
  // 获取列表
  list: (folderId?: string, filter: 'all' | 'folder' = 'all') => 
    getFavoriteList(folderId, filter, getAuthHeaders()),
  
  // 文件夹操作
  folder: {
    create: (parentId?: string) => 
      createFolder({ parent_id: parentId || null }, getAuthHeaders()),
    
    move: (favoriteId: string, folderId?: string) => 
      moveToFolder({ favorite_id: favoriteId, folder_id: folderId || null }, getAuthHeaders()),
    
    delete: (folderId: string) => 
      deleteFolder(folderId, getAuthHeaders()),
    
    rename: (folderId: string, name: string) => 
      renameFolder({ folder_id: folderId, name }, getAuthHeaders())
  }
}

// 默认导出
export default favoriteApi
