{"openapi": "3.1.0", "info": {"title": "收藏相关接口", "version": "0.1.0"}, "servers": [{"url": "/api/favorite"}], "paths": {"/api/v1/favorite/add/{profile_id}": {"post": {"summary": "添加收藏", "operationId": "添加收藏_api_v1_favorite_add__profile_id__post", "parameters": [{"name": "profile_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "搜索接口返回的profile.id", "title": "Profile Id"}, "description": "搜索接口返回的profile.id"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_TalentFavorite_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/remove/{favorite_id}": {"delete": {"summary": "移除收藏", "operationId": "移除收藏_api_v1_favorite_remove__favorite_id__delete", "parameters": [{"name": "favorite_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "收藏接口返回的favorite.id", "title": "Favorite Id"}, "description": "收藏接口返回的favorite.id"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_bool_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/reorder": {"post": {"summary": "重新排序", "operationId": "重新排序_api_v1_favorite_reorder_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteReorderBody"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_bool_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/list": {"get": {"summary": "获取文件夹中的内容", "operationId": "获取文件夹中的内容_api_v1_favorite_list_get", "parameters": [{"name": "folder_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "收藏接口返回的favorite.id。如果不传，默认是根文件夹", "title": "Folder Id"}, "description": "收藏接口返回的favorite.id。如果不传，默认是根文件夹"}, {"name": "filter", "in": "query", "required": false, "schema": {"enum": ["all", "folder"], "type": "string", "description": "过滤类型，必须小写。默认是all，列出所有人才卡片和文件夹，当传folder时，只列出文件夹", "default": "all", "title": "Filter"}, "description": "过滤类型，必须小写。默认是all，列出所有人才卡片和文件夹，当传folder时，只列出文件夹"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_list_dict__"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/folder": {"post": {"summary": "创建文件夹", "operationId": "创建文件夹_api_v1_favorite_folder_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteCreateFolderBody"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_TalentFavorite_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"summary": "移动到文件夹", "operationId": "移动到文件夹_api_v1_favorite_folder_patch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteMoveToFolderBody"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_TalentFavorite_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/folder/{folder_id}": {"delete": {"summary": "删除文件夹", "operationId": "删除文件夹_api_v1_favorite_folder__folder_id__delete", "parameters": [{"name": "folder_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "文件夹ID，值是favorite.id。文件夹必须为空才能删除，否则报400错误", "title": "Folder Id"}, "description": "文件夹ID，值是favorite.id。文件夹必须为空才能删除，否则报400错误"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_bool_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/favorite/folder/rename": {"post": {"summary": "重命名文件夹", "description": "文件夹的名字在1-32个字符以内，可以重名", "operationId": "重命名文件夹_api_v1_favorite_folder_rename_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteRenameFolderBody"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel_TalentFavorite_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"FavoriteCreateFolderBody": {"properties": {"parent_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Parent Id", "description": "父文件夹ID，值是favorite.id。如果不传，默认是在根文件夹下创建新文件夹"}}, "type": "object", "title": "FavoriteCreateFolderBody"}, "FavoriteMoveToFolderBody": {"properties": {"favorite_id": {"type": "string", "format": "uuid", "title": "Favorite Id", "description": "要移动的对象，可以是人才卡片或文件夹"}, "folder_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Folder Id", "description": "目标文件夹。如果不传，默认移动到根文件夹"}}, "type": "object", "required": ["favorite_id"], "title": "FavoriteMoveToFolderBody"}, "FavoriteRenameFolderBody": {"properties": {"folder_id": {"type": "string", "format": "uuid", "title": "Folder Id", "description": "要重命名的文件夹ID，值为favorite.id。文件夹可以重名"}, "name": {"type": "string", "maxLength": 32, "minLength": 1, "title": "Name", "description": "新的文件夹名称"}}, "type": "object", "required": ["folder_id", "name"], "title": "FavoriteRenameFolderBody"}, "FavoriteReorderBody": {"properties": {"favorite_id": {"type": "string", "format": "uuid", "title": "Favorite Id", "description": "收藏接口返回的favorite.id"}, "order": {"type": "integer", "minimum": 0.0, "title": "Order", "description": "拖拽后的新位置，值必须大于等于0"}}, "type": "object", "required": ["favorite_id", "order"], "title": "FavoriteReorderBody"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ResponseModel_TalentFavorite_": {"properties": {"data": {"anyOf": [{"$ref": "#/components/schemas/TalentFavorite"}, {"type": "null"}]}}, "type": "object", "required": ["data"], "title": "ResponseModel[TalentFavorite]"}, "ResponseModel_bool_": {"properties": {"data": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["data"], "title": "ResponseModel[bool]"}, "ResponseModel_list_dict__": {"properties": {"data": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["data"], "title": "ResponseModel[list[dict]]"}, "TalentFavorite": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "owner_id": {"type": "string", "title": "Owner Id"}, "creator_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creator Id"}, "parent_id": {"type": "string", "format": "uuid", "title": "Parent Id", "default": "00000000-0000-0000-0000-000000000000"}, "is_folder": {"type": "boolean", "title": "Is Folder", "default": false}, "level": {"type": "integer", "title": "Level", "default": 0}, "order": {"type": "integer", "title": "Order"}, "folder_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Folder Name"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["owner_id", "order"], "title": "TalentFavorite", "description": "人才收藏表\n\n注册用户收藏的人才卡片的相关记录"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}