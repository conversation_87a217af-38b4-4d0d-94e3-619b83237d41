<template>
  <div class="min-h-screen p-6 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-8 text-gray-900 dark:text-white">收藏API测试页面</h1>

      <!-- 快捷操作 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">快捷操作</h2>
        <div class="flex gap-4 flex-wrap">
          <button
            @click="fillSampleData"
            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            填充示例数据
          </button>
          <button
            @click="clearAllResults"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            清空所有结果
          </button>
          <button
            @click="exportResults"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            导出测试结果
          </button>
        </div>
      </div>

      <!-- 认证设置 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">认证设置</h2>
        <div class="flex gap-4">
          <input
            v-model="authToken"
            type="text"
            placeholder="请输入Bearer Token"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <button
            @click="saveToken"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            保存Token
          </button>
        </div>
      </div>

      <!-- API测试区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- 1. 添加收藏 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">1. 添加收藏</h3>
          <div class="space-y-3">
            <input
              v-model="addFavorite.profileId"
              type="text"
              placeholder="Profile ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testAddFavorite"
              :disabled="loading.addFavorite"
              class="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
            >
              {{ loading.addFavorite ? '请求中...' : '添加收藏' }}
            </button>
            <div v-if="results.addFavorite" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.addFavorite, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 2. 移除收藏 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">2. 移除收藏</h3>
          <div class="space-y-3">
            <input
              v-model="removeFavorite.favoriteId"
              type="text"
              placeholder="Favorite ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testRemoveFavorite"
              :disabled="loading.removeFavorite"
              class="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50"
            >
              {{ loading.removeFavorite ? '请求中...' : '移除收藏' }}
            </button>
            <div v-if="results.removeFavorite" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.removeFavorite, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 3. 重新排序 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">3. 重新排序</h3>
          <div class="space-y-3">
            <input
              v-model="reorder.favoriteId"
              type="text"
              placeholder="Favorite ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <input
              v-model.number="reorder.order"
              type="number"
              placeholder="新的排序位置"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testReorder"
              :disabled="loading.reorder"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {{ loading.reorder ? '请求中...' : '重新排序' }}
            </button>
            <div v-if="results.reorder" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.reorder, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 4. 获取收藏列表 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">4. 获取收藏列表</h3>
          <div class="space-y-3">
            <input
              v-model="getList.folderId"
              type="text"
              placeholder="Folder ID (可选)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <select
              v-model="getList.filter"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">all (默认)</option>
              <option value="folder">folder</option>
            </select>
            <button
              @click="testGetList"
              :disabled="loading.getList"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50"
            >
              {{ loading.getList ? '请求中...' : '获取列表' }}
            </button>
            <div v-if="results.getList" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm max-h-40 overflow-y-auto">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.getList, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 5. 创建文件夹 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">5. 创建文件夹</h3>
          <div class="space-y-3">
            <input
              v-model="createFolder.parentId"
              type="text"
              placeholder="Parent ID (可选)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testCreateFolder"
              :disabled="loading.createFolder"
              class="w-full px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50"
            >
              {{ loading.createFolder ? '请求中...' : '创建文件夹' }}
            </button>
            <div v-if="results.createFolder" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.createFolder, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 6. 移动到文件夹 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">6. 移动到文件夹</h3>
          <div class="space-y-3">
            <input
              v-model="moveToFolder.favoriteId"
              type="text"
              placeholder="Favorite ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <input
              v-model="moveToFolder.folderId"
              type="text"
              placeholder="Target Folder ID (可选)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testMoveToFolder"
              :disabled="loading.moveToFolder"
              class="w-full px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 disabled:opacity-50"
            >
              {{ loading.moveToFolder ? '请求中...' : '移动到文件夹' }}
            </button>
            <div v-if="results.moveToFolder" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.moveToFolder, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 7. 删除文件夹 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">7. 删除文件夹</h3>
          <div class="space-y-3">
            <input
              v-model="deleteFolder.folderId"
              type="text"
              placeholder="Folder ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testDeleteFolder"
              :disabled="loading.deleteFolder"
              class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {{ loading.deleteFolder ? '请求中...' : '删除文件夹' }}
            </button>
            <div v-if="results.deleteFolder" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.deleteFolder, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <!-- 8. 重命名文件夹 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">8. 重命名文件夹</h3>
          <div class="space-y-3">
            <input
              v-model="renameFolder.folderId"
              type="text"
              placeholder="Folder ID (UUID)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <input
              v-model="renameFolder.name"
              type="text"
              placeholder="新文件夹名称 (1-32个字符)"
              maxlength="32"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              @click="testRenameFolder"
              :disabled="loading.renameFolder"
              class="w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50"
            >
              {{ loading.renameFolder ? '请求中...' : '重命名文件夹' }}
            </button>
            <div v-if="results.renameFolder" class="mt-3 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <pre class="text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.renameFolder, null, 2) }}</pre>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const BASE_URL = '/api/v1/favorite'

// 认证token
const authToken = ref('')

// 加载状态
const loading = reactive({
  addFavorite: false,
  removeFavorite: false,
  reorder: false,
  getList: false,
  createFolder: false,
  moveToFolder: false,
  deleteFolder: false,
  renameFolder: false
})

// 测试结果
const results = reactive({
  addFavorite: null,
  removeFavorite: null,
  reorder: null,
  getList: null,
  createFolder: null,
  moveToFolder: null,
  deleteFolder: null,
  renameFolder: null
})

// 表单数据
const addFavorite = reactive({
  profileId: 'cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be'
})

const removeFavorite = reactive({
  favoriteId: ''
})

const reorder = reactive({
  favoriteId: '',
  order: 0
})

const getList = reactive({
  folderId: '',
  filter: 'all'
})

const createFolder = reactive({
  parentId: ''
})

const moveToFolder = reactive({
  favoriteId: '',
  folderId: ''
})

const deleteFolder = reactive({
  folderId: ''
})

const renameFolder = reactive({
  folderId: '',
  name: ''
})

// 保存token到localStorage
const saveToken = () => {
  if (authToken.value) {
    localStorage.setItem('favoriteApiToken', authToken.value)
    alert('Token已保存')
  }
}

// 页面加载时从localStorage读取token
onMounted(() => {
  const savedToken = localStorage.getItem('favoriteApiToken')
  if (savedToken) {
    authToken.value = savedToken
  }
})

// 通用请求函数
const makeRequest = async (method, endpoint, data = null) => {
  if (!authToken.value) {
    alert('请先设置Bearer Token')
    return null
  }

  const config = {
    method,
    headers: {
      'Authorization': `Bearer ${authToken.value}`,
      'Content-Type': 'application/json'
    }
  }

  if (data && (method === 'POST' || method === 'PATCH')) {
    config.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, config)
    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${JSON.stringify(result)}`)
    }
    
    return result
  } catch (error) {
    console.error('API请求错误:', error)
    return { error: error.message }
  }
}

// 1. 添加收藏
const testAddFavorite = async () => {
  if (!addFavorite.profileId) {
    alert('请输入Profile ID')
    return
  }

  loading.addFavorite = true
  results.addFavorite = await makeRequest('POST', `/add/${addFavorite.profileId}`)
  loading.addFavorite = false
}

// 2. 移除收藏
const testRemoveFavorite = async () => {
  if (!removeFavorite.favoriteId) {
    alert('请输入Favorite ID')
    return
  }

  loading.removeFavorite = true
  results.removeFavorite = await makeRequest('DELETE', `/remove/${removeFavorite.favoriteId}`)
  loading.removeFavorite = false
}

// 3. 重新排序
const testReorder = async () => {
  if (!reorder.favoriteId) {
    alert('请输入Favorite ID')
    return
  }

  loading.reorder = true
  results.reorder = await makeRequest('POST', '/reorder', {
    favorite_id: reorder.favoriteId,
    order: reorder.order
  })
  loading.reorder = false
}

// 4. 获取收藏列表
const testGetList = async () => {
  loading.getList = true

  const params = new URLSearchParams()
  if (getList.folderId) params.append('folder_id', getList.folderId)
  if (getList.filter) params.append('filter', getList.filter)

  const queryString = params.toString()
  const endpoint = queryString ? `/list?${queryString}` : '/list'

  results.getList = await makeRequest('GET', endpoint)
  loading.getList = false
}

// 5. 创建文件夹
const testCreateFolder = async () => {
  loading.createFolder = true
  results.createFolder = await makeRequest('POST', '/folder', {
    parent_id: createFolder.parentId || null
  })
  loading.createFolder = false
}

// 6. 移动到文件夹
const testMoveToFolder = async () => {
  if (!moveToFolder.favoriteId) {
    alert('请输入Favorite ID')
    return
  }

  loading.moveToFolder = true
  results.moveToFolder = await makeRequest('PATCH', '/folder', {
    favorite_id: moveToFolder.favoriteId,
    folder_id: moveToFolder.folderId || null
  })
  loading.moveToFolder = false
}

// 7. 删除文件夹
const testDeleteFolder = async () => {
  if (!deleteFolder.folderId) {
    alert('请输入Folder ID')
    return
  }

  loading.deleteFolder = true
  results.deleteFolder = await makeRequest('DELETE', `/folder/${deleteFolder.folderId}`)
  loading.deleteFolder = false
}

// 8. 重命名文件夹
const testRenameFolder = async () => {
  if (!renameFolder.folderId) {
    alert('请输入Folder ID')
    return
  }
  if (!renameFolder.name || renameFolder.name.length < 1 || renameFolder.name.length > 32) {
    alert('文件夹名称必须是1-32个字符')
    return
  }

  loading.renameFolder = true
  results.renameFolder = await makeRequest('POST', '/folder/rename', {
    folder_id: renameFolder.folderId,
    name: renameFolder.name
  })
  loading.renameFolder = false
}

// 快捷操作函数
const fillSampleData = () => {
  addFavorite.profileId = 'cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be'
  removeFavorite.favoriteId = 'da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49'
  reorder.favoriteId = 'da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49'
  reorder.order = 5
  getList.folderId = ''
  getList.filter = 'all'
  createFolder.parentId = ''
  moveToFolder.favoriteId = 'da65b678-47b7-4a0e-9dc2-5b0ecc8c1b49'
  moveToFolder.folderId = ''
  deleteFolder.folderId = ''
  renameFolder.folderId = ''
  renameFolder.name = 'AI研究者'
  alert('示例数据已填充')
}

const clearAllResults = () => {
  Object.keys(results).forEach(key => {
    results[key] = null
  })
  alert('所有结果已清空')
}

const exportResults = () => {
  const exportData = {
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    results: { ...results }
  }

  const dataStr = JSON.stringify(exportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `favorite-api-test-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
</script>
