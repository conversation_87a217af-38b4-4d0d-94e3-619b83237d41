<template>
  <div class="min-h-screen p-6 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-3xl font-bold mb-8 text-gray-900 dark:text-white">代理配置测试</h1>
      
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">测试代理路径</h2>
        
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">1. 收藏API代理测试</h3>
            <button
              @click="testFavoriteProxy"
              :disabled="loading.favorite"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {{ loading.favorite ? '测试中...' : '测试 /api/v1/favorite/list' }}
            </button>
            <div v-if="results.favorite" class="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <div class="text-green-600 dark:text-green-400" v-if="results.favorite.success">
                ✅ 代理配置正常
              </div>
              <div class="text-red-600 dark:text-red-400" v-else>
                ❌ 代理配置异常: {{ results.favorite.error }}
              </div>
              <pre class="mt-2 text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.favorite, null, 2) }}</pre>
            </div>
          </div>

          <div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">2. 搜索API代理测试（对比）</h3>
            <button
              @click="testSearchProxy"
              :disabled="loading.search"
              class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {{ loading.search ? '测试中...' : '测试 /api/v1/talent/search' }}
            </button>
            <div v-if="results.search" class="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <div class="text-green-600 dark:text-green-400" v-if="results.search.success">
                ✅ 搜索API代理正常
              </div>
              <div class="text-red-600 dark:text-red-400" v-else>
                ❌ 搜索API代理异常: {{ results.search.error }}
              </div>
              <pre class="mt-2 text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.search, null, 2) }}</pre>
            </div>
          </div>

          <div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">3. 直接外部API测试（无代理）</h3>
            <button
              @click="testDirectAPI"
              :disabled="loading.direct"
              class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              {{ loading.direct ? '测试中...' : '测试直接访问 search.dinq.io' }}
            </button>
            <div v-if="results.direct" class="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              <div class="text-green-600 dark:text-green-400" v-if="results.direct.success">
                ✅ 直接访问正常
              </div>
              <div class="text-red-600 dark:text-red-400" v-else>
                ❌ 直接访问失败: {{ results.direct.error }}
              </div>
              <pre class="mt-2 text-gray-800 dark:text-gray-200">{{ JSON.stringify(results.direct, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
          <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">代理配置说明</h4>
          <ul class="text-blue-800 dark:text-blue-200 text-sm space-y-1">
            <li>• 本地路径 <code>/api/v1/favorite/*</code> 代理到 <code>https://search.dinq.io/api/v1/favorite/*</code></li>
            <li>• 自动处理CORS问题</li>
            <li>• 开发和生产环境都使用相同的代理配置</li>
            <li>• 如果代理失败，检查nuxt.config.ts中的routeRules配置</li>
          </ul>
        </div>
      </div>

      <div class="text-center">
        <NuxtLink 
          to="/test"
          class="inline-block px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          返回测试中心
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 加载状态
const loading = reactive({
  favorite: false,
  search: false,
  direct: false
})

// 测试结果
const results = reactive({
  favorite: null,
  search: null,
  direct: null
})

// 测试收藏API代理
const testFavoriteProxy = async () => {
  loading.favorite = true
  try {
    const response = await fetch('/api/v1/favorite/list', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      results.favorite = {
        success: true,
        status: response.status,
        data: data
      }
    } else {
      results.favorite = {
        success: false,
        status: response.status,
        error: `HTTP ${response.status}: ${response.statusText}`
      }
    }
  } catch (error) {
    results.favorite = {
      success: false,
      error: error.message
    }
  }
  loading.favorite = false
}

// 测试搜索API代理（对比）
const testSearchProxy = async () => {
  loading.search = true
  try {
    const response = await fetch('/api/v1/talent/search?query=test', {
      method: 'GET'
    })
    
    if (response.ok) {
      const data = await response.json()
      results.search = {
        success: true,
        status: response.status,
        data: data
      }
    } else {
      results.search = {
        success: false,
        status: response.status,
        error: `HTTP ${response.status}: ${response.statusText}`
      }
    }
  } catch (error) {
    results.search = {
      success: false,
      error: error.message
    }
  }
  loading.search = false
}

// 测试直接外部API访问
const testDirectAPI = async () => {
  loading.direct = true
  try {
    const response = await fetch('https://search.dinq.io/api/v1/favorite/list', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      results.direct = {
        success: true,
        status: response.status,
        data: data
      }
    } else {
      results.direct = {
        success: false,
        status: response.status,
        error: `HTTP ${response.status}: ${response.statusText}`
      }
    }
  } catch (error) {
    results.direct = {
      success: false,
      error: error.message
    }
  }
  loading.direct = false
}

// 设置页面元数据
useHead({
  title: '代理配置测试 - Dinq',
  meta: [
    { name: 'description', content: '测试收藏API代理配置是否正常工作' }
  ]
})
</script>
