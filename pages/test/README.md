# 收藏API测试页面使用说明

## 概述

这个测试页面是为了测试收藏API接口而创建的，基于 `收藏API接口文档.md` 中的接口规范。

## 访问方式

1. 启动开发服务器：`npm run dev`
2. 访问测试中心：http://localhost:3001/test
3. 直接访问收藏API测试：http://localhost:3001/test/FavoriteApiTest

## 代理配置

收藏API已配置代理转发，所有请求通过Nuxt代理到 `https://search.dinq.io`：
- 本地路径：`/api/v1/favorite/*`
- 代理目标：`https://search.dinq.io/api/v1/favorite/*`
- 自动处理CORS问题

## 功能特性

### 1. 认证设置
- 支持Bearer Token认证
- Token会自动保存到localStorage
- 所有API请求都会使用设置的Token

### 2. API测试功能

#### 添加收藏
- **接口**: `POST /api/v1/favorite/add/{profile_id}`
- **参数**: Profile ID (UUID格式)
- **示例**: `cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be`

#### 移除收藏
- **接口**: `DELETE /api/v1/favorite/remove/{favorite_id}`
- **参数**: Favorite ID (UUID格式)

#### 重新排序
- **接口**: `POST /api/v1/favorite/reorder`
- **参数**: Favorite ID 和 新的排序位置

#### 获取收藏列表
- **接口**: `GET /api/v1/favorite/list`
- **参数**: 
  - folder_id (可选)
  - filter: all/folder

#### 创建文件夹
- **接口**: `POST /api/v1/favorite/folder`
- **参数**: parent_id (可选)

#### 移动到文件夹
- **接口**: `PATCH /api/v1/favorite/folder`
- **参数**: favorite_id 和 folder_id

#### 删除文件夹
- **接口**: `DELETE /api/v1/favorite/folder/{folder_id}`
- **参数**: Folder ID (UUID格式)

#### 重命名文件夹
- **接口**: `POST /api/v1/favorite/folder/rename`
- **参数**: folder_id 和 name (1-32个字符)

### 3. 便捷功能

#### 填充示例数据
- 一键填充所有表单的示例数据
- 基于接口文档中的示例UUID

#### 清空所有结果
- 清空所有API测试的响应结果

#### 导出测试结果
- 将所有测试结果导出为JSON文件
- 包含时间戳和基础URL信息

## 使用步骤

1. **设置认证**
   - 在"认证设置"区域输入有效的Bearer Token
   - 点击"保存Token"按钮

2. **填充示例数据**
   - 点击"填充示例数据"按钮快速填充表单

3. **测试API**
   - 选择要测试的API功能
   - 填写必要的参数
   - 点击对应的测试按钮

4. **查看结果**
   - 每个API的响应结果会显示在对应区域
   - 支持JSON格式的美化显示

5. **导出结果**
   - 测试完成后可以导出所有结果为JSON文件

## 注意事项

1. **Token安全**
   - 请确保使用有效的Bearer Token
   - 不要在生产环境中暴露Token

2. **UUID格式**
   - 所有ID参数必须是有效的UUID格式
   - 示例：`cc1d75b6-aadb-40d9-a8b6-ccd25f13a2be`

3. **文件夹操作**
   - 删除文件夹前确保文件夹为空
   - 文件夹名称长度限制在1-32个字符

4. **错误处理**
   - API错误会显示在结果区域
   - 常见错误码：401(认证失败)、400(参数错误)、404(资源不存在)

## 开发说明

### 文件结构
```
pages/test/
├── FavoriteApiTest.vue  # 收藏API测试页面
├── index.vue           # 测试中心首页
└── README.md           # 使用说明
```

### 技术栈
- Vue 3 Composition API
- Nuxt 3
- Tailwind CSS
- Fetch API

### 扩展功能
如需添加新的API测试功能，可以参考现有的实现模式：
1. 在表单数据中添加新的reactive对象
2. 在loading和results中添加对应的状态
3. 创建对应的测试函数
4. 在模板中添加UI组件

## 相关文件

- `收藏API接口文档.md` - 完整的API接口文档
- `pages/search/TalentFavorites.vue` - 实际使用收藏功能的页面
- `pages/search/SearchApiTest.vue` - 搜索API测试页面
