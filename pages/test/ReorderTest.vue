<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <h1 class="text-3xl font-bold mb-6 text-center">Reorder API 测试页面</h1>

    <!-- 后端逻辑说明 -->
    <div class="mb-6 p-4 border rounded-lg bg-blue-50 dark:bg-blue-900">
      <h2 class="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-200">📚 后端排序逻辑</h2>
      <div class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
        <p><strong>自动连续化：</strong>后端会自动保持order值连续（0,1,2,3,4,5...）</p>
        <p><strong>示例：</strong>6个项目[0,1,2,3,4,5]，把位置1移动到位置4 → 位置2,3,4自动变成1,2,3</p>
        <p><strong>前端逻辑：</strong>直接发送目标位置索引作为order值即可</p>
      </div>
    </div>
    
    <!-- 认证Token输入 -->
    <div class="mb-6 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
      <h2 class="text-xl font-semibold mb-3">🔑 认证设置</h2>
      <div class="flex gap-2">
        <input
          v-model="authToken"
          type="text"
          placeholder="输入Bearer Token (Firebase UID)"
          class="flex-1 px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          @click="saveToken"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          保存Token
        </button>
      </div>
    </div>

    <!-- 当前收藏列表 -->
    <div class="mb-6 p-4 border rounded-lg">
      <div class="flex justify-between items-center mb-3">
        <h2 class="text-xl font-semibold">📋 当前收藏列表</h2>
        <button
          @click="loadFavorites"
          :disabled="loading.loadList"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {{ loading.loadList ? '加载中...' : '刷新列表' }}
        </button>
      </div>
      
      <div v-if="favoritesList.length > 0" class="space-y-2">
        <div
          v-for="(item, index) in favoritesList"
          :key="item.id"
          class="flex items-center justify-between p-3 border rounded bg-white dark:bg-gray-700"
        >
          <div class="flex items-center gap-3">
            <span class="font-mono text-sm bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
              位置: {{ index }}
            </span>
            <span class="font-mono text-sm bg-blue-200 dark:bg-blue-600 px-2 py-1 rounded">
              Order: {{ item.order }}
            </span>
            <span class="font-semibold">
              {{ item.is_folder ? '📁' : '👤' }} {{ item.name }}
            </span>
          </div>
          <div class="flex gap-2">
            <button
              @click="selectItemForReorder(item, index)"
              class="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
            >
              选择重排序
            </button>
          </div>
        </div>
      </div>
      
      <div v-else-if="!loading.loadList" class="text-gray-500 text-center py-4">
        暂无收藏数据，请先加载列表
      </div>
    </div>

    <!-- 重排序测试 -->
    <div class="mb-6 p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-3">🔄 重排序测试</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium mb-1">选中的项目</label>
          <input
            v-model="reorderTest.selectedItem"
            type="text"
            readonly
            placeholder="请从上方列表选择项目"
            class="w-full px-3 py-2 border rounded bg-gray-100"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Favorite ID</label>
          <input
            v-model="reorderTest.favoriteId"
            type="text"
            placeholder="项目ID"
            class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium mb-1">当前位置</label>
          <input
            v-model="reorderTest.currentPosition"
            type="number"
            readonly
            class="w-full px-3 py-2 border rounded bg-gray-100"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">当前Order值</label>
          <input
            v-model="reorderTest.currentOrder"
            type="number"
            readonly
            class="w-full px-3 py-2 border rounded bg-gray-100"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">新的Order值</label>
          <input
            v-model="reorderTest.newOrder"
            type="number"
            placeholder="输入新的order值"
            class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <!-- 快速测试按钮 -->
      <div class="mb-4">
        <h3 class="text-lg font-medium mb-2">快速测试</h3>
        <div class="flex flex-wrap gap-2 mb-2">
          <button
            @click="setOrderValue(0)"
            class="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
          >
            移到最前 (order=0)
          </button>
          <button
            @click="setOrderValue(1)"
            class="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
          >
            移到第二 (order=1)
          </button>
          <button
            @click="setOrderValue(favoritesList.length - 1)"
            class="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
          >
            移到最后 (order={{ favoritesList.length - 1 }})
          </button>
          <button
            @click="setOrderValue(reorderTest.currentPosition)"
            class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            保持当前位置 (order={{ reorderTest.currentPosition }})
          </button>
        </div>

        <!-- 智能测试建议 -->
        <div v-if="reorderTest.currentPosition !== ''" class="text-sm text-gray-600 dark:text-gray-400">
          <p><strong>建议测试：</strong></p>
          <div class="flex flex-wrap gap-2 mt-1">
            <button
              v-if="reorderTest.currentPosition > 0"
              @click="setOrderValue(reorderTest.currentPosition - 1)"
              class="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
            >
              向前移动1位 (order={{ reorderTest.currentPosition - 1 }})
            </button>
            <button
              v-if="reorderTest.currentPosition < favoritesList.length - 1"
              @click="setOrderValue(parseInt(reorderTest.currentPosition) + 1)"
              class="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
            >
              向后移动1位 (order={{ parseInt(reorderTest.currentPosition) + 1 }})
            </button>
            <button
              v-if="reorderTest.currentPosition > 1"
              @click="setOrderValue(0)"
              class="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
            >
              移到开头 (验证连续化)
            </button>
          </div>
        </div>
      </div>

      <div class="flex gap-2">
        <button
          @click="testReorder"
          :disabled="loading.reorder || !reorderTest.favoriteId || reorderTest.newOrder === ''"
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          {{ loading.reorder ? '重排序中...' : '执行重排序' }}
        </button>
        <button
          @click="clearReorderTest"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          清空
        </button>
      </div>
    </div>

    <!-- 操作日志 -->
    <div v-if="operationLog.length > 0" class="mb-6 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
      <div class="flex justify-between items-center mb-3">
        <h2 class="text-xl font-semibold">📝 操作日志</h2>
        <button
          @click="operationLog = []"
          class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          清空日志
        </button>
      </div>
      <div class="space-y-1 max-h-40 overflow-y-auto">
        <div
          v-for="(log, index) in operationLog"
          :key="index"
          class="text-sm flex items-center gap-2"
          :class="log.success ? 'text-green-700 dark:text-green-400' : 'text-red-700 dark:text-red-400'"
        >
          <span class="font-mono text-xs bg-gray-200 dark:bg-gray-600 px-1 rounded min-w-[30px] text-center">
            {{ operationLog.length - index }}
          </span>
          <span class="font-mono text-xs text-gray-500 min-w-[60px]">
            {{ log.time }}
          </span>
          <span>{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="mb-6 p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-3">📊 测试结果</h2>
      <div class="space-y-3">
        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="p-3 border rounded"
          :class="result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'"
        >
          <div class="flex justify-between items-start mb-2">
            <span class="font-medium">
              {{ result.success ? '✅' : '❌' }} 测试 #{{ testResults.length - index }}
            </span>
            <span class="text-sm text-gray-500">{{ result.timestamp }}</span>
          </div>
          <div class="text-sm space-y-1">
            <div><strong>项目:</strong> {{ result.itemName }}</div>
            <div><strong>操作:</strong> 从位置 {{ result.fromPosition }} (order={{ result.fromOrder }}) 
                 → 新order={{ result.newOrder }}</div>
            <div v-if="result.success" class="text-green-600">
              <strong>结果:</strong> {{ result.message }}
            </div>
            <div v-else class="text-red-600">
              <strong>错误:</strong> {{ result.error }}
            </div>
          </div>
        </div>
      </div>
      <button
        @click="testResults = []"
        class="mt-3 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
      >
        清空结果
      </button>
    </div>

    <!-- API响应详情 -->
    <div v-if="lastApiResponse" class="p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-3">🔍 最新API响应</h2>
      <pre class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto max-h-60">{{ JSON.stringify(lastApiResponse, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 基础配置
const BASE_URL = '/api/v1/favorite'
const authToken = ref('')
const loading = reactive({
  loadList: false,
  reorder: false
})

// 收藏列表数据
const favoritesList = ref([])
const lastApiResponse = ref(null)

// 重排序测试数据
const reorderTest = reactive({
  selectedItem: '',
  favoriteId: '',
  currentPosition: '',
  currentOrder: '',
  newOrder: ''
})

// 测试结果
const testResults = ref([])

// 操作日志
const operationLog = ref([])

// 通用API请求函数
const makeRequest = async (method, endpoint, data = null) => {
  if (!authToken.value) {
    throw new Error('请先设置Bearer Token')
  }

  const config = {
    method,
    headers: {
      'Authorization': `Bearer ${authToken.value}`,
      'Content-Type': 'application/json'
    }
  }

  if (data && (method === 'POST' || method === 'PATCH')) {
    config.body = JSON.stringify(data)
  }

  const response = await fetch(`${BASE_URL}${endpoint}`, config)
  const result = await response.json()
  
  lastApiResponse.value = result
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${JSON.stringify(result)}`)
  }
  
  return result
}

// 保存token到localStorage
const saveToken = () => {
  if (authToken.value) {
    localStorage.setItem('reorderTestToken', authToken.value)
    alert('Token已保存')
  }
}

// 加载收藏列表
const loadFavorites = async () => {
  loading.loadList = true
  try {
    const response = await makeRequest('GET', '/list?filter=all')
    if (response.data && Array.isArray(response.data)) {
      // 扁平化处理，包含文件夹和其中的卡片
      const flatList = []
      
      response.data.forEach(item => {
        if (item.is_folder) {
          flatList.push({
            id: item.id,
            name: item.folder_name || 'Unnamed Folder',
            order: item.order,
            is_folder: true,
            level: 0
          })
          
          // 添加文件夹中的卡片
          if (item.children && Array.isArray(item.children)) {
            item.children.forEach(child => {
              if (!child.is_folder) {
                flatList.push({
                  id: child.id,
                  name: child.profile?.name || 'Unknown',
                  order: child.order,
                  is_folder: false,
                  level: 1,
                  parent_id: item.id
                })
              }
            })
          }
        } else {
          // 根级别的卡片
          flatList.push({
            id: item.id,
            name: item.profile?.name || 'Unknown',
            order: item.order,
            is_folder: false,
            level: 0
          })
        }
      })
      
      // 按order排序
      flatList.sort((a, b) => (a.order || 0) - (b.order || 0))
      favoritesList.value = flatList
    }
  } catch (err) {
    console.error('Failed to load favorites:', err)
    alert(`加载失败: ${err.message}`)
  } finally {
    loading.loadList = false
  }
}

// 选择项目进行重排序
const selectItemForReorder = (item, index) => {
  reorderTest.selectedItem = `${item.is_folder ? '📁' : '👤'} ${item.name}`
  reorderTest.favoriteId = item.id
  reorderTest.currentPosition = index
  reorderTest.currentOrder = item.order
  reorderTest.newOrder = ''
}

// 设置order值
const setOrderValue = (value) => {
  reorderTest.newOrder = value
}

// 清空重排序测试
const clearReorderTest = () => {
  Object.assign(reorderTest, {
    selectedItem: '',
    favoriteId: '',
    currentPosition: '',
    currentOrder: '',
    newOrder: ''
  })
}

// 执行重排序测试
const testReorder = async () => {
  loading.reorder = true

  const testRecord = {
    timestamp: new Date().toLocaleTimeString(),
    itemName: reorderTest.selectedItem,
    fromPosition: reorderTest.currentPosition,
    fromOrder: reorderTest.currentOrder,
    newOrder: reorderTest.newOrder,
    success: false,
    message: '',
    error: ''
  }

  try {
    const response = await makeRequest('POST', '/reorder', {
      favorite_id: reorderTest.favoriteId,
      order: parseInt(reorderTest.newOrder)
    })

    testRecord.success = true
    testRecord.message = '重排序成功'

    // 添加到操作日志
    operationLog.value.unshift({
      time: new Date().toLocaleTimeString(),
      message: `✅ ${reorderTest.selectedItem} 从位置${reorderTest.currentPosition}(order=${reorderTest.currentOrder}) → order=${reorderTest.newOrder}`,
      success: true
    })

    // 重新加载列表查看结果
    await loadFavorites()

  } catch (err) {
    console.error('Reorder failed:', err)
    testRecord.error = err.message

    // 添加失败日志
    operationLog.value.unshift({
      time: new Date().toLocaleTimeString(),
      message: `❌ ${reorderTest.selectedItem} 重排序失败: ${err.message}`,
      success: false
    })
  } finally {
    testResults.value.unshift(testRecord)
    loading.reorder = false
  }
}

// 页面加载时从localStorage读取token
onMounted(() => {
  const savedToken = localStorage.getItem('reorderTestToken')
  if (savedToken) {
    authToken.value = savedToken
  }
})
</script>

<style scoped>
.container {
  min-height: 100vh;
}
</style>
