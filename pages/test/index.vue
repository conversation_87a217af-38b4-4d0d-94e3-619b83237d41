<template>
  <div class="min-h-screen p-6 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-8 text-gray-900 dark:text-white">API测试中心</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- 收藏API测试 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">收藏API</h2>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            测试收藏功能的所有API接口，包括添加、删除、排序、文件夹管理等功能。
          </p>
          <div class="space-y-2 mb-4">
            <div class="text-sm text-gray-500 dark:text-gray-400">• 添加/移除收藏</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 重新排序</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 文件夹管理</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 获取收藏列表</div>
          </div>
          <div class="space-y-2">
            <NuxtLink
              to="/test/FavoriteApiTest"
              class="inline-block w-full text-center px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
            >
              完整API测试
            </NuxtLink>
            <NuxtLink
              to="/test/ReorderTest"
              class="inline-block w-full text-center px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
            >
              排序专项测试
            </NuxtLink>
          </div>
        </div>

        <!-- 搜索API测试 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">搜索API</h2>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            测试人才搜索、网络分析、邮箱查询等核心搜索功能API。
          </p>
          <div class="space-y-2 mb-4">
            <div class="text-sm text-gray-500 dark:text-gray-400">• 人才搜索</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 网络分析</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 邮箱查询</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 推荐系统</div>
          </div>
          <NuxtLink 
            to="/search/SearchApiTest"
            class="inline-block w-full text-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            开始测试
          </NuxtLink>
        </div>

        <!-- 其他测试页面 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">其他测试</h2>
          </div>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            其他功能测试页面，包括网络分析、OG图片生成等。
          </p>
          <div class="space-y-2 mb-4">
            <div class="text-sm text-gray-500 dark:text-gray-400">• 网络分析测试</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• OG图片测试</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• 比较功能测试</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">• HTML2Canvas测试</div>
          </div>
          <div class="space-y-2">
            <NuxtLink
              to="/test/ProxyTest"
              class="inline-block w-full text-center px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              代理测试
            </NuxtLink>
            <NuxtLink
              to="/test-network"
              class="inline-block w-full text-center px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              网络测试
            </NuxtLink>
            <NuxtLink
              to="/test-og"
              class="inline-block w-full text-center px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              OG测试
            </NuxtLink>
          </div>
        </div>

      </div>

      <!-- 快速访问链接 -->
      <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">快速访问</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <NuxtLink 
            to="/search"
            class="text-center p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <div class="text-sm font-medium text-gray-900 dark:text-white">搜索页面</div>
          </NuxtLink>
          <NuxtLink 
            to="/compare"
            class="text-center p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <div class="text-sm font-medium text-gray-900 dark:text-white">比较页面</div>
          </NuxtLink>
          <NuxtLink 
            to="/github"
            class="text-center p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <div class="text-sm font-medium text-gray-900 dark:text-white">GitHub分析</div>
          </NuxtLink>
          <NuxtLink 
            to="/"
            class="text-center p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <div class="text-sm font-medium text-gray-900 dark:text-white">首页</div>
          </NuxtLink>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">使用说明</h3>
        <ul class="text-blue-800 dark:text-blue-200 space-y-1 text-sm">
          <li>• 收藏API测试需要先设置Bearer Token</li>
          <li>• 搜索API测试需要登录认证</li>
          <li>• 测试结果可以导出为JSON文件</li>
          <li>• 建议在开发环境中使用这些测试页面</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面元数据
useHead({
  title: 'API测试中心 - Dinq',
  meta: [
    { name: 'description', content: 'Dinq API测试中心，测试各种API接口功能' }
  ]
})
</script>
